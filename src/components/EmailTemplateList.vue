<template>
  <div class="app-container">
    <!-- 左侧控制面板 -->
    <div class="control-panel">
      <div class="panel-header">
        <h1 class="app-title">
          <el-icon class="title-icon"><Document /></el-icon>
          HTML邮件模板预览
        </h1>
        <div class="app-description">
          高效预览和管理HTML邮件模板
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <div class="search-label">
          <el-icon><Search /></el-icon>
          <span>搜索模板</span>
        </div>
        <el-input
          v-model="searchTerm"
          placeholder="输入模板名称或路径..."
          class="search-input"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stats-card">
          <div class="stat-item">
            <div class="stat-label">总模板数</div>
            <div class="stat-value">{{ totalTemplates }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">分类数</div>
            <div class="stat-value">{{ sortedCategories.length }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">当前显示</div>
            <div class="stat-value">{{ totalTemplates }}</div>
          </div>
        </div>
      </div>

      <!-- 分类列表 -->
      <div class="categories-section">
        <div class="section-title">
          <el-icon><Folder /></el-icon>
          <span>模板分类</span>
        </div>
        <div class="categories-list">
          <div
            v-for="category in sortedCategories"
            :key="category"
            class="category-item"
            @click="scrollToCategory(category)"
          >
            <div class="category-name">{{ category }}</div>
            <div class="category-count">{{ categorizedTemplates[category].length }}</div>
          </div>
        </div>
      </div>

      <!-- 使用提示 -->
      <div class="performance-tip">
        <el-icon><InfoFilled /></el-icon>
        <div class="tip-content">
          <div class="tip-title">快速导航</div>
          <div class="tip-text">点击左侧分类可快速跳转到对应模板区域</div>
        </div>
      </div>
    </div>

    <!-- 右侧虚拟滚动区域 -->
    <div class="content-area">
      <div class="content-header">
        <div class="content-title">
          <span>模板预览</span>
          <el-tag v-if="searchTerm" type="warning" closable @close="searchTerm = ''">
            搜索: {{ searchTerm }}
          </el-tag>
        </div>
      </div>

      <div class="virtual-scroll-container" style="padding: 20px 10px 20px 20px;">
        <!-- 使用优化的虚拟滚动组件 -->
        <VirtualScrollGrid
          v-if="totalTemplates > 0"
          ref="virtualScrollRef"
          :categorized-data="categorizedTemplates"
          :item-height="360"
          :category-height="60"
          :columns-per-row="3"
          :gap="20"
          :vertical-gap="20"
          :buffer-size="1.5"
        >
          <template #default="{ template }">
            <TemplateCard :template="template" />
          </template>
        </VirtualScrollGrid>
        
        <!-- 搜索无结果时的提示 -->
        <div v-else-if="searchTerm" class="no-results-container">
          <el-icon><Search /></el-icon>
          <div class="no-results-content">
            <h3>没有找到匹配的模板</h3>
            <p>尝试使用不同的关键词搜索，或者清除搜索条件查看所有模板</p>
            <el-button @click="searchTerm = ''" type="primary">清除搜索</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 性能监控面板 -->
    <PerformancePanel />
  </div>
</template>

<script>
import TemplateCard from './TemplateCard.vue'
import VirtualScrollGrid from './VirtualScrollGrid.vue'
import PerformancePanel from './PerformancePanel.vue'
import templatesData from '../templates.json'
import { InfoFilled, Search, Loading, Document, Folder } from '@element-plus/icons-vue'

export default {
  name: 'EmailTemplateList',
  components: {
    TemplateCard,
    VirtualScrollGrid,
    PerformancePanel,
    InfoFilled,
    Search,
    Loading,
    Document,
    Folder,
  },
  data() {
    return {
      searchTerm: '',
      templates: templatesData
    }
  },
  computed: {
    templatesWithCategories() {
      return this.templates.map(template => ({
        ...template,
        category: this.extractCategoryFromPath(template.path)
      }))
    },
    filteredTemplates() {
      if (!this.searchTerm) {
        return this.templatesWithCategories
      }
      const searchLower = this.searchTerm.toLowerCase()
      return this.templatesWithCategories.filter(template => 
        template.name.toLowerCase().includes(searchLower) ||
        template.path.toLowerCase().includes(searchLower) ||
        template.category.toLowerCase().includes(searchLower)
      )
    },
    categorizedTemplates() {
      const categories = {}
      this.filteredTemplates.forEach(template => {
        if (!categories[template.category]) {
          categories[template.category] = []
        }
        categories[template.category].push(template)
      })
      return categories
    },
    sortedCategories() {
      return Object.keys(this.categorizedTemplates).sort()
    },
    totalTemplates() {
      return this.filteredTemplates.length
    }
  },
  methods: {
    extractCategoryFromPath(path) {
      // 从路径中提取目录名作为分类
      // 例如: '/cbi/account-suspended/pb/index.html' -> 'account-suspended'
      const parts = path.split('/')
      if (parts.length >= 2 && parts[1] === 'cbi') {
        return parts[2]
      }
      return '其他'
    },
    handleSearch() {
      // 搜索处理已经通过computed属性自动处理
      // 搜索后重置分类引用
      this.$nextTick(() => {
        this.categoryRefs = {}
      })
    },

    scrollToCategory(category) {
      // 使用虚拟滚动组件的滚动方法
      if (this.$refs.virtualScrollRef && this.$refs.virtualScrollRef.scrollToCategory) {
        this.$refs.virtualScrollRef.scrollToCategory(category)
        console.log(`点击分类: ${category}`)
      } else {
        console.warn('虚拟滚动组件引用不可用')
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  display: flex;
  height: 100vh;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 左侧控制面板 */
.control-panel {
  width: 350px;
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止整个面板滚动 */
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
  height: 100vh;
}

.panel-header {
  padding: 24px 20px 20px;
  border-bottom: 1px solid #f3f4f6;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.app-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.title-icon {
  font-size: 24px;
}

.app-description {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

.search-section {
  padding: 20px;
  border-bottom: 1px solid #f3f4f6;
  flex-shrink: 0; /* 防止被压缩 */
}

.search-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.search-input {
  width: 100%;
}

.stats-section {
  padding: 20px;
  border-bottom: 1px solid #f3f4f6;
  flex-shrink: 0; /* 防止被压缩 */
}

.stats-card {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 12px 6px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}



.categories-section {
  padding: 16px 20px 8px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex子项收缩 */
  overflow: hidden; /* 防止整个区域溢出 */
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  flex-shrink: 0; /* 防止标题被压缩 */
}

.categories-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0; /* 允许flex子项收缩 */
  padding-right: 8px; /* 为滚动条留出空间 */
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  margin-bottom: 6px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  cursor: pointer;
  user-select: none;
  min-width: 0; /* 防止内容溢出 */
}

.category-item:hover {
  background: #e0f2fe;
  border-color: #0ea5e9;
  transform: translateX(2px);
}

.category-item:active {
  transform: translateX(1px);
  background: #bae6fd;
}

.category-name {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
}

.category-count {
  font-size: 12px;
  color: #6b7280;
  background: #e5e7eb;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
  flex-shrink: 0; /* 防止数字被压缩 */
  min-width: 24px;
  text-align: center;
}

.performance-tip {
  padding: 12px 20px;
  background: #eff6ff;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 10px;
  align-items: flex-start;
  flex-shrink: 0; /* 防止被压缩 */
  margin-top: auto; /* 推到底部 */
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 14px;
  font-weight: 500;
  color: #1e40af;
  margin-bottom: 4px;
}

.tip-text {
  font-size: 12px;
  color: #3730a3;
  line-height: 1.5;
}

/* 右侧内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  overflow: hidden;
}

.content-header {
  padding: 14px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-shrink: 0; /* 防止头部被压缩 */
}

.content-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
  min-width: 0; /* 防止溢出 */
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0; /* 防止控制按钮被压缩 */
}

.virtual-scroll-container {
  flex: 1;
  overflow-y: auto;
  background: #f8fafc;
  min-height: 0; /* 允许flex子项收缩 */
}

.template-list {
  padding: 24px;
}

.category-section {
  margin-bottom: 40px;
}

.category-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e5e7eb;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.no-results-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 60px;
  text-align: center;
  color: #6b7280;
}

.no-results-container .el-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.5;
}

.no-results-content h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #374151;
}

.no-results-content p {
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 24px 0;
  max-width: 400px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .control-panel {
    width: 300px;
  }

  .stats-card {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .stat-item {
    padding: 8px 6px;
  }

  .stat-value {
    font-size: 18px;
  }
}

@media (max-width: 1024px) {
  .control-panel {
    width: 280px;
  }

  .panel-header {
    padding: 20px 16px 16px;
  }

  .search-section,
  .stats-section,
  .categories-section {
    padding-left: 16px;
    padding-right: 16px;
  }

  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
    height: auto;
  }

  .control-panel {
    width: 100%;
    height: auto;
    max-height: 300px;
    overflow-y: auto;
  }

  .panel-header {
    padding: 16px;
  }

  .app-title {
    font-size: 18px;
  }

  .search-section,
  .stats-section {
    padding: 12px 16px;
  }

  .categories-section {
    padding: 12px 16px 8px;
    max-height: 120px;
  }

  .performance-tip {
    padding: 8px 16px;
    margin-top: 0;
  }

  .content-area {
    height: calc(100vh - 320px);
    min-height: 400px;
  }

  .content-header {
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .content-title {
    font-size: 16px;
    justify-content: center;
  }

  .template-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .control-panel {
    max-height: 250px;
  }

  .panel-header {
    padding: 12px;
  }

  .app-title {
    font-size: 16px;
    gap: 8px;
  }

  .title-icon {
    font-size: 20px;
  }

  .app-description {
    font-size: 12px;
  }

  .search-section,
  .stats-section {
    padding: 8px 12px;
  }

  .stats-card {
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
  }

  .stat-item {
    padding: 6px 4px;
  }

  .stat-label {
    font-size: 10px;
  }

  .stat-value {
    font-size: 14px;
  }

  .categories-section {
    padding: 8px 12px 6px;
    max-height: 100px;
  }

  .section-title {
    font-size: 12px;
    margin-bottom: 8px;
  }

  .category-item {
    padding: 8px 10px;
    margin-bottom: 4px;
  }

  .category-name {
    font-size: 12px;
  }

  .category-count {
    font-size: 10px;
    padding: 1px 6px;
  }

  .performance-tip {
    padding: 6px 12px;
  }

  .tip-title {
    font-size: 12px;
  }

  .tip-text {
    font-size: 10px;
  }

  .content-area {
    height: calc(100vh - 270px);
    min-height: 350px;
  }

  .content-header {
    padding: 10px 12px;
  }

  .content-title {
    font-size: 14px;
  }
}

/* 自定义滚动条 */
.control-panel::-webkit-scrollbar,
.virtual-scroll-container::-webkit-scrollbar,
.categories-list::-webkit-scrollbar {
  width: 6px;
}

.control-panel::-webkit-scrollbar-track,
.virtual-scroll-container::-webkit-scrollbar-track,
.categories-list::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.control-panel::-webkit-scrollbar-thumb,
.virtual-scroll-container::-webkit-scrollbar-thumb,
.categories-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.control-panel::-webkit-scrollbar-thumb:hover,
.virtual-scroll-container::-webkit-scrollbar-thumb:hover,
.categories-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style> 