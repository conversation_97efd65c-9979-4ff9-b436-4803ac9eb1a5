<template>
  <div class="performance-panel" v-if="showPanel">
    <div class="panel-header">
      <div class="panel-title">
        <span class="title-icon">📊</span>
        <span>性能监控</span>
      </div>
      <div class="panel-controls">
        <button 
          @click="toggleMonitoring" 
          :class="['control-btn', isMonitoring ? 'stop' : 'start']"
        >
          {{ isMonitoring ? '⏹️ 停止' : '▶️ 开始' }}
        </button>
        <button @click="clearData" class="control-btn clear">
          🗑️ 清除
        </button>
        <button @click="togglePanel" class="control-btn close">
          ❌
        </button>
      </div>
    </div>

    <div class="panel-content" v-if="metrics">
      <div class="metrics-grid">
        <div class="metric-item">
          <div class="metric-label">FPS</div>
          <div class="metric-value" :class="getFpsClass(metrics.fps)">
            {{ formatValue(metrics.fps, 1) }}
          </div>
        </div>

        <div class="metric-item">
          <div class="metric-label">内存使用</div>
          <div class="metric-value">
            {{ formatValue(metrics.memoryUsage, 1, 'MB') }}
          </div>
        </div>

        <div class="metric-item">
          <div class="metric-label">滚动事件</div>
          <div class="metric-value">
            {{ metrics.scrollEvents || 0 }}
          </div>
        </div>

        <div class="metric-item">
          <div class="metric-label">可见项目</div>
          <div class="metric-value">
            {{ metrics.visibleItems || 0 }}
          </div>
        </div>

        <div class="metric-item">
          <div class="metric-label">平均渲染时间</div>
          <div class="metric-value">
            {{ formatValue(metrics.averageRenderTime, 2, 'ms') }}
          </div>
        </div>

        <div class="metric-item">
          <div class="metric-label">监控时长</div>
          <div class="metric-value">
            {{ formatDuration(metrics.monitoringDuration) }}
          </div>
        </div>
      </div>

      <div class="performance-chart" v-if="chartData.length > 0">
        <div class="chart-title">FPS 趋势图</div>
        <div class="chart-container">
          <svg class="fps-chart" viewBox="0 0 300 100">
            <polyline
              :points="chartPoints"
              fill="none"
              stroke="#3b82f6"
              stroke-width="2"
            />
            <!-- 60 FPS 基准线 -->
            <line x1="0" y1="20" x2="300" y2="20" stroke="#10b981" stroke-width="1" stroke-dasharray="5,5" opacity="0.5"/>
            <text x="5" y="15" font-size="10" fill="#10b981">60 FPS</text>
            <!-- 30 FPS 基准线 -->
            <line x1="0" y1="50" x2="300" y2="50" stroke="#f59e0b" stroke-width="1" stroke-dasharray="5,5" opacity="0.5"/>
            <text x="5" y="45" font-size="10" fill="#f59e0b">30 FPS</text>
          </svg>
        </div>
      </div>
    </div>

    <div class="panel-footer">
      <div class="status-indicator" :class="{ active: isMonitoring }">
        {{ isMonitoring ? '🟢 监控中' : '🔴 已停止' }}
      </div>
      <div class="last-update">
        最后更新: {{ lastUpdateTime }}
      </div>
    </div>
  </div>

  <!-- 浮动按钮 -->
  <button 
    v-if="!showPanel" 
    @click="togglePanel" 
    class="floating-btn"
    title="打开性能监控面板"
  >
    📊
  </button>
</template>

<script>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import performanceMonitor from '../utils/performanceMonitor.js'

export default {
  name: 'PerformancePanel',
  setup() {
    const showPanel = ref(false)
    const isMonitoring = ref(false)
    const metrics = ref(null)
    const chartData = ref([])
    const lastUpdateTime = ref('')
    let updateInterval = null

    // 计算图表点位
    const chartPoints = computed(() => {
      if (chartData.value.length === 0) return ''
      
      const maxPoints = 50
      const data = chartData.value.slice(-maxPoints)
      const width = 300
      const height = 100
      
      return data.map((fps, index) => {
        const x = (index / (data.length - 1)) * width
        const y = height - (fps / 60) * height // 假设最大60 FPS
        return `${x},${Math.max(0, Math.min(height, y))}`
      }).join(' ')
    })

    // 更新性能数据
    const updateMetrics = () => {
      const currentMetrics = performanceMonitor.getRealTimeMetrics()
      console.log('📊 更新性能数据:', currentMetrics) // 调试信息

      if (currentMetrics) {
        metrics.value = currentMetrics

        // 更新图表数据
        if (currentMetrics.fps !== undefined && currentMetrics.fps !== null) {
          chartData.value.push(currentMetrics.fps)
          if (chartData.value.length > 100) {
            chartData.value = chartData.value.slice(-50)
          }
        }

        lastUpdateTime.value = new Date().toLocaleTimeString()
      } else {
        console.log('⚠️ 性能数据为空，监控状态:', performanceMonitor.isMonitoring)
      }

      isMonitoring.value = performanceMonitor.isMonitoring
    }

    // 切换监控状态
    const toggleMonitoring = () => {
      if (performanceMonitor.isMonitoring) {
        performanceMonitor.stop()
      } else {
        performanceMonitor.start()
      }
      updateMetrics()
    }

    // 清除数据
    const clearData = () => {
      chartData.value = []
      metrics.value = null
      lastUpdateTime.value = ''
    }

    // 切换面板显示
    const togglePanel = () => {
      showPanel.value = !showPanel.value
      
      if (showPanel.value) {
        updateMetrics()
        // 开始定期更新
        updateInterval = setInterval(updateMetrics, 1000)
      } else {
        // 停止定期更新
        if (updateInterval) {
          clearInterval(updateInterval)
          updateInterval = null
        }
      }
    }

    // 获取FPS颜色类
    const getFpsClass = (fps) => {
      if (!fps) return ''
      if (fps >= 55) return 'good'
      if (fps >= 30) return 'warning'
      return 'poor'
    }

    // 格式化数值
    const formatValue = (value, decimals = 1, unit = '') => {
      if (value === undefined || value === null) return 'N/A'
      const formatted = typeof value === 'number' ? value.toFixed(decimals) : value
      return unit ? `${formatted} ${unit}` : formatted
    }

    // 格式化持续时间
    const formatDuration = (ms) => {
      if (!ms || ms === 0) return '0s'
      const seconds = Math.floor(ms / 1000)
      const minutes = Math.floor(seconds / 60)
      if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`
      }
      return `${seconds}s`
    }

    // 检查是否在开发环境
    const checkDevMode = () => {
      const isDev = process.env.NODE_ENV === 'development' || 
                   window.location.search.includes('debug=true') ||
                   window.location.search.includes('perf=true')
      
      if (isDev) {
        // 在开发环境中自动显示浮动按钮
        setTimeout(() => {
          if (!showPanel.value) {
            // 可以选择自动打开面板
            // togglePanel()
          }
        }, 2000)
      }
    }

    onMounted(() => {
      checkDevMode()
    })

    onUnmounted(() => {
      if (updateInterval) {
        clearInterval(updateInterval)
      }
    })

    return {
      showPanel,
      isMonitoring,
      metrics,
      chartData,
      chartPoints,
      lastUpdateTime,
      toggleMonitoring,
      clearData,
      togglePanel,
      getFpsClass,
      formatValue,
      formatDuration
    }
  }
}
</script>

<style scoped>
.performance-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 350px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 12px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 14px;
}

.title-icon {
  font-size: 16px;
}

.panel-controls {
  display: flex;
  gap: 6px;
}

.control-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  font-size: 10px;
  transition: background 0.2s;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.control-btn.start {
  background: rgba(34, 197, 94, 0.8);
}

.control-btn.stop {
  background: rgba(239, 68, 68, 0.8);
}

.panel-content {
  padding: 16px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.metric-item {
  background: #f8fafc;
  padding: 8px 10px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.metric-label {
  font-size: 10px;
  color: #6b7280;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.metric-value.good {
  color: #059669;
}

.metric-value.warning {
  color: #d97706;
}

.metric-value.poor {
  color: #dc2626;
}

.performance-chart {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.chart-title {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.chart-container {
  background: #f8fafc;
  border-radius: 6px;
  padding: 8px;
}

.fps-chart {
  width: 100%;
  height: 80px;
}

.panel-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  border-radius: 0 0 12px 12px;
  font-size: 10px;
}

.status-indicator {
  color: #6b7280;
}

.status-indicator.active {
  color: #059669;
}

.last-update {
  color: #9ca3af;
}

.floating-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  transition: transform 0.2s, box-shadow 0.2s;
}

.floating-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .performance-panel {
    width: calc(100vw - 40px);
    max-width: 350px;
  }
  
  .floating-btn {
    bottom: 80px;
    right: 15px;
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
}
</style>
