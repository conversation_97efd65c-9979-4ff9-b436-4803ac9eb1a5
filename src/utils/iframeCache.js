/**
 * 高级iframe缓存管理器
 * 支持DOM元素缓存和状态管理
 */
class AdvancedIframeCache {
  constructor() {
    this.cache = new Map()
    this.maxSize = 50 // 最大缓存数量，减少内存占用
    this.cleanupInterval = null
    this.startCleanupTimer()
  }

  /**
   * 检查是否有缓存
   */
  has(path) {
    return this.cache.has(path)
  }

  /**
   * 获取缓存项
   */
  get(path) {
    const item = this.cache.get(path)
    if (item) {
      // 更新访问时间
      item.lastAccessed = Date.now()
      item.accessCount++
      return item
    }
    return null
  }

  /**
   * 设置缓存项
   */
  set(path, data) {
    // 如果缓存已满，删除最久未访问的项目
    if (this.cache.size >= this.maxSize) {
      this.evictLeastRecentlyUsed()
    }

    const existingItem = this.cache.get(path)
    const cacheItem = {
      path,
      isVisible: data.isVisible || false,
      isLoaded: data.isLoaded || false,
      iframeElement: data.iframeElement || null,
      lastAccessed: Date.now(),
      accessCount: existingItem ? existingItem.accessCount + 1 : 1,
      createdAt: existingItem ? existingItem.createdAt : Date.now()
    }

    this.cache.set(path, cacheItem)
    console.log(`缓存iframe: ${path}, 总缓存数: ${this.cache.size}`)
  }

  /**
   * 删除最久未访问的缓存项
   */
  evictLeastRecentlyUsed() {
    let oldestPath = null
    let oldestTime = Date.now()
    
    for (const [key, value] of this.cache.entries()) {
      if (value.lastAccessed < oldestTime) {
        oldestTime = value.lastAccessed
        oldestPath = key
      }
    }
    
    if (oldestPath) {
      const item = this.cache.get(oldestPath)
      // 清理DOM元素
      if (item && item.iframeElement && item.iframeElement.parentNode) {
        item.iframeElement.parentNode.removeChild(item.iframeElement)
      }
      this.cache.delete(oldestPath)
      console.log(`清理缓存: ${oldestPath}`)
    }
  }

  /**
   * 缓存iframe DOM元素
   */
  cacheIframeElement(path, iframeElement) {
    const existingItem = this.get(path)
    if (existingItem) {
      existingItem.iframeElement = iframeElement
      existingItem.isLoaded = true
      this.set(path, existingItem)
    } else {
      this.set(path, {
        isVisible: true,
        isLoaded: true,
        iframeElement: iframeElement
      })
    }
  }

  /**
   * 获取缓存的iframe元素
   */
  getCachedIframe(path) {
    const item = this.get(path)
    return item && item.iframeElement ? item.iframeElement : null
  }

  /**
   * 更新可见状态
   */
  updateVisibility(path, isVisible) {
    const item = this.get(path)
    if (item) {
      item.isVisible = isVisible
      this.set(path, item)
    } else {
      this.set(path, { isVisible, isLoaded: false })
    }
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now()
    const maxAge = 10 * 60 * 1000 // 10分钟
    const itemsToDelete = []

    for (const [key, value] of this.cache.entries()) {
      if (now - value.lastAccessed > maxAge) {
        itemsToDelete.push(key)
      }
    }

    itemsToDelete.forEach(key => {
      const item = this.cache.get(key)
      if (item && item.iframeElement && item.iframeElement.parentNode) {
        item.iframeElement.parentNode.removeChild(item.iframeElement)
      }
      this.cache.delete(key)
    })

    if (itemsToDelete.length > 0) {
      console.log(`清理过期缓存: ${itemsToDelete.length} 项`)
    }
  }

  /**
   * 启动定期清理
   */
  startCleanupTimer() {
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000) // 每5分钟清理一次
  }

  /**
   * 停止定期清理
   */
  stopCleanupTimer() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
  }

  /**
   * 清空所有缓存
   */
  clear() {
    // 清理所有DOM元素
    for (const [key, value] of this.cache.entries()) {
      if (value.iframeElement && value.iframeElement.parentNode) {
        value.iframeElement.parentNode.removeChild(value.iframeElement)
      }
    }
    this.cache.clear()
    console.log('清空所有iframe缓存')
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const stats = {
      totalItems: this.cache.size,
      loadedItems: 0,
      visibleItems: 0,
      memoryUsage: 0
    }

    for (const [key, value] of this.cache.entries()) {
      if (value.isLoaded) stats.loadedItems++
      if (value.isVisible) stats.visibleItems++
      if (value.iframeElement) {
        // 估算内存使用（简单估算）
        stats.memoryUsage += 1024 // 假设每个iframe占用1KB
      }
    }

    return stats
  }

  /**
   * 销毁缓存管理器
   */
  destroy() {
    this.stopCleanupTimer()
    this.clear()
  }
}

// 创建全局实例
const iframeCache = new AdvancedIframeCache()

// 在页面卸载时清理缓存
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    iframeCache.destroy()
  })
}

export default iframeCache
