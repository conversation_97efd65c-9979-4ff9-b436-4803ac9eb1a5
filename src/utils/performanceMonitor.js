/**
 * 性能监控工具
 * 用于监控虚拟滚动组件的性能指标
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      scrollEvents: 0,
      renderTime: [],
      visibleItems: [],
      memoryUsage: [],
      fps: [],
      lastFrameTime: 0,
      frameCount: 0
    }
    
    this.isMonitoring = false
    this.rafId = null
    this.startTime = 0
  }

  start() {
    if (this.isMonitoring) return

    this.isMonitoring = true
    this.startTime = performance.now()
    this.metrics = {
      scrollEvents: 0,
      renderTime: [],
      visibleItems: [],
      memoryUsage: [],
      fps: [],
      lastFrameTime: this.startTime,
      frameCount: 0
    }

    this.monitorFPS()
    console.log('🚀 性能监控已启动', {
      startTime: this.startTime,
      hasMemoryAPI: !!performance.memory
    })
  }

  stop() {
    if (!this.isMonitoring) return
    
    this.isMonitoring = false
    if (this.rafId) {
      cancelAnimationFrame(this.rafId)
    }
    
    this.generateReport()
  }

  // 记录滚动事件
  recordScrollEvent() {
    if (!this.isMonitoring) return
    this.metrics.scrollEvents++

    // 每100次滚动事件输出一次调试信息
    if (this.metrics.scrollEvents % 100 === 0) {
      console.log('📜 滚动事件计数:', this.metrics.scrollEvents)
    }
  }

  // 记录渲染时间
  recordRenderTime(startTime, endTime) {
    if (!this.isMonitoring) return
    const renderTime = endTime - startTime
    this.metrics.renderTime.push(renderTime)
    
    // 只保留最近100次记录
    if (this.metrics.renderTime.length > 100) {
      this.metrics.renderTime.shift()
    }
  }

  // 记录可见项目数量
  recordVisibleItems(count) {
    if (!this.isMonitoring) return
    this.metrics.visibleItems.push(count)
    
    // 只保留最近100次记录
    if (this.metrics.visibleItems.length > 100) {
      this.metrics.visibleItems.shift()
    }
  }

  // 监控FPS
  monitorFPS() {
    if (!this.isMonitoring) return

    const now = performance.now()
    this.metrics.frameCount++

    if (now - this.metrics.lastFrameTime >= 1000) {
      const fps = this.metrics.frameCount
      this.metrics.fps.push(fps)
      this.metrics.frameCount = 0
      this.metrics.lastFrameTime = now

      // 输出FPS调试信息
      console.log('🎯 当前FPS:', fps)

      // 只保留最近60秒的FPS记录
      if (this.metrics.fps.length > 60) {
        this.metrics.fps.shift()
      }
    }

    this.rafId = requestAnimationFrame(() => this.monitorFPS())
  }

  // 记录内存使用情况
  recordMemoryUsage() {
    if (!this.isMonitoring || !performance.memory) return
    
    const memory = {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit,
      timestamp: performance.now()
    }
    
    this.metrics.memoryUsage.push(memory)
    
    // 只保留最近50次记录
    if (this.metrics.memoryUsage.length > 50) {
      this.metrics.memoryUsage.shift()
    }
  }

  // 生成性能报告
  generateReport() {
    const duration = performance.now() - this.startTime
    const avgRenderTime = this.metrics.renderTime.length > 0 
      ? this.metrics.renderTime.reduce((a, b) => a + b, 0) / this.metrics.renderTime.length 
      : 0
    const maxRenderTime = this.metrics.renderTime.length > 0 
      ? Math.max(...this.metrics.renderTime) 
      : 0
    const avgVisibleItems = this.metrics.visibleItems.length > 0 
      ? this.metrics.visibleItems.reduce((a, b) => a + b, 0) / this.metrics.visibleItems.length 
      : 0
    const avgFPS = this.metrics.fps.length > 0 
      ? this.metrics.fps.reduce((a, b) => a + b, 0) / this.metrics.fps.length 
      : 0
    const minFPS = this.metrics.fps.length > 0 ? Math.min(...this.metrics.fps) : 0

    const report = {
      duration: Math.round(duration),
      scrollEvents: this.metrics.scrollEvents,
      scrollEventsPerSecond: Math.round(this.metrics.scrollEvents / (duration / 1000)),
      avgRenderTime: Math.round(avgRenderTime * 100) / 100,
      maxRenderTime: Math.round(maxRenderTime * 100) / 100,
      avgVisibleItems: Math.round(avgVisibleItems),
      avgFPS: Math.round(avgFPS),
      minFPS,
      memorySnapshots: this.metrics.memoryUsage.length
    }

    console.group('📊 虚拟滚动性能报告')
    console.log('⏱️  监控时长:', `${report.duration}ms`)
    console.log('📜 滚动事件总数:', report.scrollEvents)
    console.log('🔄 滚动频率:', `${report.scrollEventsPerSecond}/秒`)
    console.log('🎨 平均渲染时间:', `${report.avgRenderTime}ms`)
    console.log('⚠️  最大渲染时间:', `${report.maxRenderTime}ms`)
    console.log('👁️  平均可见项目:', report.avgVisibleItems)
    console.log('🎯 平均FPS:', report.avgFPS)
    console.log('📉 最低FPS:', report.minFPS)
    console.log('💾 内存快照数:', report.memorySnapshots)
    
    // 性能建议
    if (report.maxRenderTime > 16) {
      console.warn('⚠️  检测到渲染时间过长，可能影响流畅度')
    }
    if (report.minFPS < 30) {
      console.warn('⚠️  检测到FPS过低，建议优化')
    }
    if (report.scrollEventsPerSecond > 60) {
      console.warn('⚠️  滚动事件频率过高，建议增加节流')
    }
    
    console.groupEnd()
    
    return report
  }

  // 获取实时性能数据
  getRealTimeMetrics() {
    if (!this.isMonitoring) return null

    const currentTime = performance.now()
    const monitoringDuration = currentTime - this.startTime

    // 计算当前FPS
    const currentFPS = this.metrics.fps.length > 0 ? this.metrics.fps[this.metrics.fps.length - 1] : undefined

    // 计算平均渲染时间
    const recentRenderTime = this.metrics.renderTime.slice(-10)
    const averageRenderTime = recentRenderTime.length > 0
      ? recentRenderTime.reduce((a, b) => a + b, 0) / recentRenderTime.length
      : undefined

    // 计算内存使用
    let memoryUsage = undefined
    if (performance.memory) {
      memoryUsage = performance.memory.usedJSHeapSize / (1024 * 1024) // 转换为MB
    }

    // 获取最新的可见项目数
    const visibleItems = this.metrics.visibleItems.length > 0
      ? this.metrics.visibleItems[this.metrics.visibleItems.length - 1]
      : undefined

    return {
      fps: currentFPS,
      averageRenderTime: averageRenderTime ? Math.round(averageRenderTime * 100) / 100 : undefined,
      memoryUsage: memoryUsage ? Math.round(memoryUsage * 10) / 10 : undefined,
      scrollEvents: this.metrics.scrollEvents,
      visibleItems: visibleItems,
      monitoringDuration: Math.round(monitoringDuration)
    }
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

// 开发环境下自动启用
if (process.env.NODE_ENV === 'development') {
  // 添加全局快捷键（使用更不容易冲突的组合）
  if (typeof window !== 'undefined') {
    let keySequence = []
    let sequenceTimeout = null

    window.addEventListener('keydown', (e) => {
      // 防止与浏览器快捷键冲突
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.isContentEditable) {
        return
      }

      // 使用连续按键序列：Alt+P+M (Performance Monitor)
      if (e.altKey && e.key === 'p') {
        e.preventDefault()
        keySequence = ['p']
        clearTimeout(sequenceTimeout)
        sequenceTimeout = setTimeout(() => {
          keySequence = []
        }, 2000) // 2秒内完成序列
        return
      }

      // 第二个键：M (Monitor)
      if (keySequence.length === 1 && keySequence[0] === 'p' && e.key === 'm') {
        e.preventDefault()
        keySequence.push('m')

        // 开始/停止监控
        if (performanceMonitor.isMonitoring) {
          performanceMonitor.stop()
          console.log('🛑 性能监控已停止')
        } else {
          performanceMonitor.start()
          console.log('▶️ 性能监控已开始')
        }

        keySequence = []
        clearTimeout(sequenceTimeout)
        return
      }

      // 使用连续按键序列：Alt+P+R (Performance Report)
      if (keySequence.length === 1 && keySequence[0] === 'p' && e.key === 'r') {
        e.preventDefault()
        const metrics = performanceMonitor.getRealTimeMetrics()
        if (metrics) {
          console.log('📊 实时性能数据:', metrics)
          // 在页面上显示一个临时通知
          showPerformanceNotification(metrics)
        }

        keySequence = []
        clearTimeout(sequenceTimeout)
        return
      }

      // 使用F12作为备用快捷键（通常不会冲突）
      if (e.key === 'F12' && e.shiftKey) {
        e.preventDefault()
        if (performanceMonitor.isMonitoring) {
          performanceMonitor.stop()
          console.log('🛑 性能监控已停止 (F12)')
        } else {
          performanceMonitor.start()
          console.log('▶️ 性能监控已开始 (F12)')
        }
        return
      }

      // 重置序列如果按了其他键
      if (keySequence.length > 0 && !['p', 'm', 'r'].includes(e.key)) {
        keySequence = []
        clearTimeout(sequenceTimeout)
      }
    })

    // 显示性能通知的函数
    function showPerformanceNotification(metrics) {
      // 创建临时通知元素
      const notification = document.createElement('div')
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        font-family: monospace;
        font-size: 12px;
        z-index: 10000;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        transition: opacity 0.3s ease;
      `

      notification.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 8px;">📊 性能监控数据</div>
        <div>FPS: ${metrics.fps?.toFixed(1) || 'N/A'}</div>
        <div>内存: ${metrics.memoryUsage?.toFixed(1) || 'N/A'} MB</div>
        <div>滚动事件: ${metrics.scrollEvents || 0}</div>
        <div>可见项目: ${metrics.visibleItems || 0}</div>
        <div>渲染时间: ${metrics.averageRenderTime?.toFixed(2) || 'N/A'} ms</div>
      `

      document.body.appendChild(notification)

      // 3秒后自动移除
      setTimeout(() => {
        notification.style.opacity = '0'
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification)
          }
        }, 300)
      }, 3000)
    }

    console.log('🔧 性能监控快捷键:')
    console.log('  Alt+P+M: 开始/停止监控 (连续按键)')
    console.log('  Alt+P+R: 显示实时数据 (连续按键)')
    console.log('  Shift+F12: 开始/停止监控 (备用)')
    console.log('  💡 提示: 连续按键需要在2秒内完成')
  }
}

export default performanceMonitor
